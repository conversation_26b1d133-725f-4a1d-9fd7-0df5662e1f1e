package mux

import (
	"bytes"
	"encoding/json"
	"net/http"
	"strings"
	"testing"
)

// 自定义JSON编码器，添加前缀
func customJSONEncoder(v interface{}) ([]byte, error) {
	data, err := json.Marshal(v)
	if err != nil {
		return nil, err
	}
	// 添加自定义前缀
	result := append([]byte(`{"custom":true,"data":`), data...)
	result = append(result, '}')
	return result, nil
}

// 自定义JSON解码器，处理带前缀的数据
func customJSONDecoder(data []byte, v interface{}) error {
	// 简单的自定义解码器，这里只是示例
	return json.Unmarshal(data, v)
}

func TestCustomJSONEncoder(t *testing.T) {
	// 创建带自定义JSON编码器的引擎
	engine := NewWithConfig(Config{
		JSONEncoder: customJSONEncoder,
		JSONDecoder: customJSONDecoder,
	})

	engine.GET("/test", func(c *Context) {
		c.<PERSON><PERSON><PERSON>(200, H{"message": "hello"})
	})

	// 创建模拟请求
	req, _ := http.NewRequest("GET", "/test", nil)
	ctx := engine.createContext(nil, req)

	// 执行处理器
	engine.handleRequest(ctx)

	// 验证响应
	expected := `{"custom":true,"data":{"message":"hello"}}`
	if string(ctx.response.body) != expected {
		t.Errorf("Expected %s, got %s", expected, string(ctx.response.body))
	}

	// 验证Content-Type
	if ctx.response.headers["Content-Type"] != "application/json; charset=utf-8" {
		t.Errorf("Expected Content-Type to be application/json; charset=utf-8")
	}
}

func TestDefaultJSONEncoder(t *testing.T) {
	// 创建默认配置的引擎
	engine := New()

	engine.GET("/test", func(c *Context) {
		c.JSON(200, H{"message": "hello"})
	})

	// 创建模拟请求
	req, _ := http.NewRequest("GET", "/test", nil)
	ctx := engine.createContext(nil, req)

	// 执行处理器
	engine.handleRequest(ctx)

	// 验证响应使用标准JSON格式
	expected := `{"message":"hello"}`
	if string(ctx.response.body) != expected {
		t.Errorf("Expected %s, got %s", expected, string(ctx.response.body))
	}
}

func TestJSONBinding(t *testing.T) {
	// 创建引擎
	engine := New()

	type TestStruct struct {
		Name string `json:"name"`
		Age  int    `json:"age"`
	}

	engine.POST("/bind", func(c *Context) {
		var data TestStruct
		if err := c.ShouldBindJSON(&data); err != nil {
			c.JSON(400, H{"error": err.Error()})
			return
		}
		c.JSON(200, H{"received": data})
	})

	// 测试有效JSON
	jsonData := `{"name":"John","age":25}`
	req, _ := http.NewRequest("POST", "/bind", strings.NewReader(jsonData))
	req.Header.Set("Content-Type", "application/json")

	ctx := engine.createContext(nil, req)
	engine.handleRequest(ctx)

	// 验证响应
	expected := `{"received":{"name":"John","age":25}}`
	if string(ctx.response.body) != expected {
		t.Errorf("Expected %s, got %s", expected, string(ctx.response.body))
	}
}

func TestJSONBindingError(t *testing.T) {
	// 创建引擎
	engine := New()

	type TestStruct struct {
		Name string `json:"name"`
		Age  int    `json:"age"`
	}

	engine.POST("/bind", func(c *Context) {
		var data TestStruct
		if err := c.BindJSON(&data); err != nil {
			return // BindJSON already handles the error response
		}
		c.JSON(200, H{"received": data})
	})

	// 测试无效JSON
	invalidJSON := `{"name":"John","age":}`
	req, _ := http.NewRequest("POST", "/bind", strings.NewReader(invalidJSON))
	req.Header.Set("Content-Type", "application/json")

	ctx := engine.createContext(nil, req)
	engine.handleRequest(ctx)

	// 验证错误响应
	if ctx.response.statusCode != 400 {
		t.Errorf("Expected status code 400, got %d", ctx.response.statusCode)
	}

	// 验证错误消息包含"error"字段
	if !bytes.Contains(ctx.response.body, []byte("error")) {
		t.Errorf("Expected error message in response body")
	}
}

func TestConfigValidation(t *testing.T) {
	// 测试只设置编码器
	engine1 := NewWithConfig(Config{
		JSONEncoder: customJSONEncoder,
	})
	if engine1.jsonEncoder == nil {
		t.Error("JSONEncoder should be set")
	}
	if engine1.jsonDecoder == nil {
		t.Error("JSONDecoder should use default")
	}

	// 测试只设置解码器
	engine2 := NewWithConfig(Config{
		JSONDecoder: customJSONDecoder,
	})
	if engine2.jsonEncoder == nil {
		t.Error("JSONEncoder should use default")
	}
	if engine2.jsonDecoder == nil {
		t.Error("JSONDecoder should be set")
	}

	// 测试空配置
	engine3 := NewWithConfig(Config{})
	if engine3.jsonEncoder == nil {
		t.Error("JSONEncoder should use default")
	}
	if engine3.jsonDecoder == nil {
		t.Error("JSONDecoder should use default")
	}
}
