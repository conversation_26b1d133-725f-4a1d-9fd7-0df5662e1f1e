# Binding API Documentation

本文档介绍了 gnet-mux 中类似 Gin 的绑定 API，支持 `go-playground/validator` 验证。

## 特性

- 🚀 类似 Gin 的绑定 API
- ✅ 支持 `go-playground/validator` 验证
- 📝 支持多种数据格式：JSON, XML, Form, Query
- 🔄 支持多重绑定（ShouldBindBodyWith）
- 🛡️ Must bind 和 Should bind 两种模式

## 支持的绑定类型

### Should Bind 方法（推荐）

这些方法在绑定失败时返回错误，由开发者处理：

- `ShouldBind()` - 根据 Content-Type 自动选择绑定类型
- `ShouldBindJSON()` - JSON 绑定
- `ShouldBindXML()` - XML 绑定
- `ShouldBindQuery()` - URL 查询参数绑定
- `ShouldBindForm()` - 表单绑定
- `ShouldBindBodyWith()` - 指定绑定类型的多重绑定

### Must Bind 方法

这些方法在绑定失败时自动返回 400 错误：

- `Bind()` - 根据 Content-Type 自动选择绑定类型
- `BindJSON()` - JSON 绑定
- `BindXML()` - XML 绑定
- `BindQuery()` - URL 查询参数绑定
- `BindForm()` - 表单绑定

## 验证标签

使用 `binding` 标签进行验证：

```go
type User struct {
    Name     string `json:"name" form:"name" binding:"required,min=2"`
    Email    string `json:"email" form:"email" binding:"required,email"`
    Age      int    `json:"age" form:"age" binding:"required,min=1,max=120"`
    Password string `json:"password" form:"password" binding:"required,min=6"`
}
```

### 常用验证标签

- `required` - 必填字段
- `min=n` - 最小长度/值
- `max=n` - 最大长度/值
- `email` - 邮箱格式
- `url` - URL 格式
- `alpha` - 只包含字母
- `alphanum` - 只包含字母和数字
- `numeric` - 只包含数字

更多验证标签请参考：https://pkg.go.dev/github.com/go-playground/validator/v10#hdr-Baked_In_Validators_and_Tags

## 使用示例

### 1. JSON 绑定

```go
func createUser(c *mux.Context) {
    var user User
    if err := c.ShouldBindJSON(&user); err != nil {
        c.JSON(http.StatusBadRequest, map[string]interface{}{
            "error": err.Error(),
        })
        return
    }
    
    // 处理用户创建逻辑
    c.JSON(http.StatusOK, map[string]interface{}{
        "message": "User created successfully",
        "user":    user,
    })
}
```

### 2. 表单绑定

```go
func loginHandler(c *mux.Context) {
    var form LoginForm
    if err := c.ShouldBind(&form); err != nil {
        c.JSON(http.StatusBadRequest, map[string]interface{}{
            "error": err.Error(),
        })
        return
    }
    
    // 处理登录逻辑
}
```

### 3. 查询参数绑定

```go
func searchHandler(c *mux.Context) {
    var params QueryParams
    if err := c.ShouldBindQuery(&params); err != nil {
        c.JSON(http.StatusBadRequest, map[string]interface{}{
            "error": err.Error(),
        })
        return
    }
    
    // 处理搜索逻辑
}
```

### 4. 多重绑定

当需要多次绑定同一个请求体时，使用 `ShouldBindBodyWith`：

```go
func multiBindHandler(c *mux.Context) {
    var objA FormA
    var objB FormB
    
    // 第一次绑定
    if err := c.ShouldBindBodyWith(&objA, mux.JSON); err == nil {
        c.String(http.StatusOK, "the body should be formA")
        return
    }
    
    // 第二次绑定（重用缓存的请求体）
    if err := c.ShouldBindBodyWith(&objB, mux.JSON); err == nil {
        c.String(http.StatusOK, "the body should be formB")
        return
    }
    
    c.JSON(http.StatusBadRequest, map[string]interface{}{
        "error": "Could not bind to any known format",
    })
}
```

### 5. Must Bind（自动错误处理）

```go
func createUserMust(c *mux.Context) {
    var user User
    // 绑定失败时自动返回 400 错误
    c.BindJSON(&user)
    
    // 如果执行到这里，说明绑定成功
    c.JSON(http.StatusOK, map[string]interface{}{
        "message": "User created successfully",
        "user":    user,
    })
}
```

## 自动绑定类型选择

`ShouldBind()` 和 `Bind()` 方法会根据请求的 Content-Type 自动选择绑定类型：

- `application/json` → JSON 绑定
- `application/xml` 或 `text/xml` → XML 绑定
- `application/x-www-form-urlencoded` → Form 绑定
- `multipart/form-data` → Multipart Form 绑定
- GET 请求 → Query 绑定

## 错误处理

绑定错误包含详细的验证信息：

```json
{
  "error": "Key: 'User.Name' Error:Field validation for 'Name' failed on the 'min' tag\nKey: 'User.Email' Error:Field validation for 'Email' failed on the 'email' tag"
}
```

## 性能注意事项

- `ShouldBindBodyWith` 会缓存请求体，对性能有轻微影响
- 对于 Query、Form、FormPost、FormMultipart，可以多次调用 `ShouldBind()` 而不影响性能
- 只有 JSON、XML、MsgPack、ProtoBuf 等需要读取请求体的格式才需要使用 `ShouldBindBodyWith`

## 完整示例

查看 `example/binding_demo.go` 文件获取完整的使用示例。