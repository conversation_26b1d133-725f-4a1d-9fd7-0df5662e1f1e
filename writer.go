package mux

import (
	"bufio"
	"fmt"
	"net"
	"net/http"
	"strings"
	"time"

	"github.com/panjf2000/gnet/v2"
)

// streamWriter implements io.Writer for streaming responses using chunked transfer encoding
type streamWriter struct {
	conn gnet.Conn
	hc   *httpCodec
}

// Write implements io.Writer interface for streaming
func (sw *streamWriter) Write(data []byte) (int, error) {
	if len(data) == 0 {
		return 0, nil
	}

	// Write chunk size in hex followed by CRLF
	chunkSize := fmt.Sprintf("%x\r\n", len(data))
	sw.hc.buf = append(sw.hc.buf, chunkSize...)

	// Write chunk data followed by CRLF
	sw.hc.buf = append(sw.hc.buf, data...)
	sw.hc.buf = append(sw.hc.buf, "\r\n"...)

	// Flush the buffer to the connection
	sw.conn.Write(sw.hc.buf)
	sw.hc.buf = sw.hc.buf[:0] // Reset buffer

	return len(data), nil
}

// writeEndChunk writes the final chunk to end the stream
func (sw *streamWriter) writeEndChunk() {
	// Write the final chunk (0-length chunk followed by CRLF)
	sw.hc.buf = append(sw.hc.buf, "0\r\n\r\n"...)
	sw.conn.Write(sw.hc.buf)
	sw.hc.buf = sw.hc.buf[:0] // Reset buffer
}

// responseWriter implements http.ResponseWriter for compatibility with http.FileServer and WebSocket upgrades
type responseWriter struct {
	ctx    *Context
	header http.Header
}

func (w *responseWriter) Header() http.Header {
	if w.header == nil {
		// Create a standard http.Header and sync it with context headers
		w.header = make(http.Header)
		// Copy existing headers from context
		for k, v := range w.ctx.response.headers {
			w.header.Set(k, v)
		}
	}
	return w.header
}

func (w *responseWriter) Write(data []byte) (int, error) {
	// Sync headers back to context before writing
	w.syncHeadersToContext()
	w.ctx.response.body = append(w.ctx.response.body, data...)
	w.ctx.writeResponse()
	return len(data), nil
}

func (w *responseWriter) WriteHeader(statusCode int) {
	// Sync headers back to context before setting status
	w.syncHeadersToContext()
	w.ctx.Status(statusCode)
}

// syncHeadersToContext syncs the http.Header back to the context's headers
func (w *responseWriter) syncHeadersToContext() {
	if w.header != nil {
		for key, values := range w.header {
			if len(values) > 0 {
				// Join multiple values with comma
				w.ctx.response.headers[key] = strings.Join(values, ", ")
			}
		}
	}
}

// Hijack implements http.Hijacker interface for WebSocket upgrades
func (w *responseWriter) Hijack() (net.Conn, *bufio.ReadWriter, error) {
	if w.ctx.conn == nil {
		return nil, nil, fmt.Errorf("connection not available")
	}

	// Create a wrapper that implements net.Conn interface
	conn := &connWrapper{gnetConn: w.ctx.conn}

	// Create bufio readers/writers for the connection
	reader := bufio.NewReader(conn)
	writer := bufio.NewWriter(conn)
	readWriter := bufio.NewReadWriter(reader, writer)

	return conn, readWriter, nil
}

// connWrapper wraps gnet.Conn to implement net.Conn interface for WebSocket upgrades
type connWrapper struct {
	gnetConn gnet.Conn
}

// Read implements net.Conn interface
func (c *connWrapper) Read(b []byte) (int, error) {
	if len(b) == 0 {
		return 0, nil
	}

	// Check how much data is available
	available := c.gnetConn.InboundBuffered()
	if available == 0 {
		// No data available, this might indicate connection closed or no data ready
		return 0, nil
	}

	// Read up to len(b) bytes or all available data, whichever is smaller
	toRead := len(b)
	if available < toRead {
		toRead = available
	}

	// Use Next() to read and consume the data from the buffer
	data, err := c.gnetConn.Next(toRead)
	if err != nil {
		return 0, err
	}

	n := copy(b, data)
	return n, nil
}

// Write implements net.Conn interface
func (c *connWrapper) Write(b []byte) (int, error) {
	return c.gnetConn.Write(b)
}

// Close implements net.Conn interface
func (c *connWrapper) Close() error {
	return c.gnetConn.Close()
}

// LocalAddr implements net.Conn interface
func (c *connWrapper) LocalAddr() net.Addr {
	return c.gnetConn.LocalAddr()
}

// RemoteAddr implements net.Conn interface
func (c *connWrapper) RemoteAddr() net.Addr {
	return c.gnetConn.RemoteAddr()
}

// SetDeadline implements net.Conn interface
func (c *connWrapper) SetDeadline(t time.Time) error {
	// gnet doesn't support deadlines in the same way, so we'll return nil
	// This is acceptable for WebSocket usage
	return nil
}

// SetReadDeadline implements net.Conn interface
func (c *connWrapper) SetReadDeadline(t time.Time) error {
	// gnet doesn't support deadlines in the same way, so we'll return nil
	// This is acceptable for WebSocket usage
	return nil
}

// SetWriteDeadline implements net.Conn interface
func (c *connWrapper) SetWriteDeadline(t time.Time) error {
	// gnet doesn't support deadlines in the same way, so we'll return nil
	// This is acceptable for WebSocket usage
	return nil
}
