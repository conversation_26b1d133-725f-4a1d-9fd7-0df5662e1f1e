package mux

import (
	"net/http"
	"strings"
	"testing"

	"google.golang.org/protobuf/types/known/anypb"
)

func TestXMLRendering(t *testing.T) {
	engine := New()

	// Create a mock request
	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := engine.createContext(nil, req)

	// Test XML rendering
	data := H{"message": "hello", "status": "ok"}
	ctx.XML(200, data)

	// Check content type
	if ctx.response.headers["Content-Type"] != "application/xml; charset=utf-8" {
		t.Errorf("Expected Content-Type to be application/xml; charset=utf-8, got %s", ctx.response.headers["Content-Type"])
	}

	// Check that body is not empty
	if len(ctx.response.body) == 0 {
		t.Error("Expected XML body to not be empty")
	}
}

func TestYAMLRendering(t *testing.T) {
	engine := New()

	// Create a mock request
	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := engine.createContext(nil, req)

	// Test YAML rendering
	data := H{"message": "hello", "status": "ok"}
	ctx.YAML(200, data)

	// Check content type
	if ctx.response.headers["Content-Type"] != "application/x-yaml; charset=utf-8" {
		t.Errorf("Expected Content-Type to be application/x-yaml; charset=utf-8, got %s", ctx.response.headers["Content-Type"])
	}

	// Check that body is not empty
	if len(ctx.response.body) == 0 {
		t.Error("Expected YAML body to not be empty")
	}
}

func TestProtoBufRendering(t *testing.T) {
	engine := New()

	// Create a mock request
	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := engine.createContext(nil, req)

	// Test ProtoBuf rendering with anypb.Any
	msg := &anypb.Any{
		TypeUrl: "type.googleapis.com/example.Test",
		Value:   []byte("test data"),
	}
	ctx.ProtoBuf(200, msg)

	// Check content type
	if ctx.response.headers["Content-Type"] != "application/x-protobuf" {
		t.Errorf("Expected Content-Type to be application/x-protobuf, got %s", ctx.response.headers["Content-Type"])
	}

	// Check that body is not empty
	if len(ctx.response.body) == 0 {
		t.Error("Expected ProtoBuf body to not be empty")
	}
}

func TestDataFromReader(t *testing.T) {
	engine := New()

	// Create a mock request
	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := engine.createContext(nil, req)

	// Test DataFromReader
	data := "Hello from reader"
	reader := strings.NewReader(data)
	extraHeaders := map[string]string{
		"Content-Disposition": `attachment; filename="test.txt"`,
		"Cache-Control":       "no-cache",
	}

	ctx.DataFromReader(http.StatusOK, int64(len(data)), "text/plain", reader, extraHeaders)

	// Check status code
	if ctx.response.statusCode != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, ctx.response.statusCode)
	}

	// Check content type
	if ctx.response.headers["Content-Type"] != "text/plain" {
		t.Errorf("Expected Content-Type to be text/plain, got %s", ctx.response.headers["Content-Type"])
	}

	// Check extra headers
	if ctx.response.headers["Content-Disposition"] != `attachment; filename="test.txt"` {
		t.Errorf("Expected Content-Disposition header to be set")
	}

	if ctx.response.headers["Cache-Control"] != "no-cache" {
		t.Errorf("Expected Cache-Control header to be set")
	}

	// Check body content
	if string(ctx.response.body) != data {
		t.Errorf("Expected body to be '%s', got '%s'", data, string(ctx.response.body))
	}
}

func TestCustomEncoders(t *testing.T) {
	// Test custom XML encoder
	customXMLEncoder := func(v interface{}) ([]byte, error) {
		return []byte("<custom>test</custom>"), nil
	}

	// Test custom YAML encoder
	customYAMLEncoder := func(v interface{}) ([]byte, error) {
		return []byte("custom: test"), nil
	}

	engine := NewWithConfig(Config{
		XMLEncoder:  customXMLEncoder,
		YAMLEncoder: customYAMLEncoder,
	})

	// Create a mock request
	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	// Test custom XML encoder
	ctx := engine.createContext(nil, req)
	ctx.XML(200, H{"test": "data"})

	if string(ctx.response.body) != "<custom>test</custom>" {
		t.Errorf("Expected custom XML output, got %s", string(ctx.response.body))
	}

	// Test custom YAML encoder
	ctx = engine.createContext(nil, req)
	ctx.YAML(200, H{"test": "data"})

	if string(ctx.response.body) != "custom: test" {
		t.Errorf("Expected custom YAML output, got %s", string(ctx.response.body))
	}
}
