package mux

import (
	"net/http"
	"testing"
)

func TestRouteParams(t *testing.T) {
	engine := New()

	// Register routes with parameters
	engine.GET("/user/:id", func(c *Context) {
		c.String(200, "user %s", c.Para<PERSON>("id"))
	})

	engine.GET("/user/:id/post/:postId", func(c *Context) {
		c.String(200, "user %s post %s", c.<PERSON>("id"), c.<PERSON>("postId"))
	})

	engine.GET("/api/v1/user/:id/profile", func(c *Context) {
		c.String(200, "profile for user %s", c.Param("id"))
	})

	// Test single parameter
	handler, params := engine.findHandlerWithParams("GET", "/user/123")
	if handler == nil {
		t.Fatal("Handler should be found for /user/123")
	}
	if params["id"] != "123" {
		t.<PERSON>rrorf("Expected id to be '123', got '%s'", params["id"])
	}

	// Test multiple parameters
	handler, params = engine.findHandlerWithParams("GET", "/user/456/post/789")
	if handler == nil {
		t.<PERSON>al("Handler should be found for /user/456/post/789")
	}
	if params["id"] != "456" {
		t.Errorf("Expected id to be '456', got '%s'", params["id"])
	}
	if params["postId"] != "789" {
		t.Errorf("Expected postId to be '789', got '%s'", params["postId"])
	}

	// Test mixed static and parameter segments
	handler, params = engine.findHandlerWithParams("GET", "/api/v1/user/999/profile")
	if handler == nil {
		t.Fatal("Handler should be found for /api/v1/user/999/profile")
	}
	if params["id"] != "999" {
		t.Errorf("Expected id to be '999', got '%s'", params["id"])
	}

	// Test non-matching route
	handler, params = engine.findHandlerWithParams("GET", "/user/123/invalid")
	if handler != nil {
		t.Fatal("Handler should not be found for /user/123/invalid")
	}
}

func TestRouteConflicts(t *testing.T) {
	engine := New()

	// Register conflicting routes
	engine.GET("/user/:id", func(c *Context) {
		c.String(200, "param route")
	})

	engine.GET("/user/admin", func(c *Context) {
		c.String(200, "static route")
	})

	// Static route should take precedence
	handler, params := engine.findHandlerWithParams("GET", "/user/admin")
	if handler == nil {
		t.Fatal("Handler should be found for /user/admin")
	}

	// Create context to test the actual response
	req, _ := http.NewRequest("GET", "/user/admin", nil)
	ctx := &Context{
		request: req,
		response: &Response{
			headers:    make(map[string]string),
			statusCode: 200,
		},
		params: make(map[string]string),
	}

	// Set parameters
	for key, value := range params {
		ctx.params[key] = value
	}

	// Parameter route should still work for other values
	handler, params = engine.findHandlerWithParams("GET", "/user/123")
	if handler == nil {
		t.Fatal("Handler should be found for /user/123")
	}
	if params["id"] != "123" {
		t.Errorf("Expected id to be '123', got '%s'", params["id"])
	}
}

func TestRootRoute(t *testing.T) {
	engine := New()

	// Register root route
	engine.GET("/", func(c *Context) {
		c.String(200, "root")
	})

	handler, params := engine.findHandlerWithParams("GET", "/")
	if handler == nil {
		t.Fatal("Handler should be found for /")
	}
	if len(params) != 0 {
		t.Errorf("Expected no parameters for root route, got %v", params)
	}
}

func TestEmptySegments(t *testing.T) {
	engine := New()

	engine.GET("/user/:id/posts", func(c *Context) {
		c.String(200, "user posts")
	})

	// Test with trailing slash
	handler, params := engine.findHandlerWithParams("GET", "/user/123/posts/")
	if handler == nil {
		t.Fatal("Handler should be found for /user/123/posts/")
	}
	if params["id"] != "123" {
		t.Errorf("Expected id to be '123', got '%s'", params["id"])
	}
}

func TestContextParams(t *testing.T) {
	engine := New()

	engine.GET("/user/:id/post/:postId", func(c *Context) {
		userID := c.Param("id")
		postID := c.Param("postId")
		c.String(200, "User: %s, Post: %s", userID, postID)
	})

	// Create a mock request
	req, _ := http.NewRequest("GET", "/user/alice/post/hello-world", nil)
	ctx := engine.createContext(nil, req)

	// Simulate the parameter extraction that would happen in handleRequest
	handler, params := engine.findHandlerWithParams("GET", "/user/alice/post/hello-world")
	if handler == nil {
		t.Fatal("Handler should be found")
	}

	// Set parameters
	for key, value := range params {
		ctx.params[key] = value
	}

	// Test parameter retrieval
	if ctx.Param("id") != "alice" {
		t.Errorf("Expected id to be 'alice', got '%s'", ctx.Param("id"))
	}
	if ctx.Param("postId") != "hello-world" {
		t.Errorf("Expected postId to be 'hello-world', got '%s'", ctx.Param("postId"))
	}

	// Test non-existent parameter
	if ctx.Param("nonexistent") != "" {
		t.Errorf("Expected empty string for non-existent parameter, got '%s'", ctx.Param("nonexistent"))
	}
}
