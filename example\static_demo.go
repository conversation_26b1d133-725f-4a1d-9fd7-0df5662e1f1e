//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"log"
	"net/http"
	"os"

	mux "gitlink.org.cn/nanakura/gnet-mux"
)

func main() {
	router := mux.Default()

	// Create demo directories and files
	setupDemoFiles()

	// Static file serving (following Gin example exactly)
	router.Static("/assets", "./assets")
	router.StaticFS("/more_static", http.Dir("my_file_system"))
	router.StaticFile("/favicon.ico", "./resources/favicon.ico")

	// Additional examples
	router.GET("/", func(c *mux.Context) {
		c.HTML(`
<!DOCTYPE html>
<html>
<head>
    <title>Static File Demo</title>
    <link rel="stylesheet" href="/assets/style.css">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
</head>
<body>
    <h1>Static File Serving Demo</h1>
    <p>This page demonstrates static file serving capabilities.</p>

    <h2>Available Static Files:</h2>
    <ul>
        <li><a href="/assets/style.css">CSS File (/assets/style.css)</a></li>
        <li><a href="/assets/script.js">JavaScript File (/assets/script.js)</a></li>
        <li><a href="/assets/image.png">Image File (/assets/image.png)</a></li>
        <li><a href="/assets/data.json">JSON File (/assets/data.json)</a></li>
        <li><a href="/more_static/config.txt">Config File (/more_static/config.txt)</a></li>
        <li><a href="/favicon.ico">Favicon (/favicon.ico)</a></li>
    </ul>

    <h2>Directory Listing:</h2>
    <ul>
        <li><a href="/assets/">Browse /assets/ directory</a></li>
        <li><a href="/more_static/">Browse /more_static/ directory</a></li>
    </ul>

    <script src="/assets/script.js"></script>
</body>
</html>
		`)
	})

	// API endpoint to list available static files
	router.GET("/api/files", func(c *mux.Context) {
		c.JSON(http.StatusOK, mux.H{
			"message": "Available static files",
			"files": []mux.H{
				{
					"path":        "/assets/style.css",
					"type":        "CSS",
					"description": "Stylesheet for the demo page",
				},
				{
					"path":        "/assets/script.js",
					"type":        "JavaScript",
					"description": "Client-side script",
				},
				{
					"path":        "/assets/image.png",
					"type":        "Image",
					"description": "Demo image file",
				},
				{
					"path":        "/assets/data.json",
					"type":        "JSON",
					"description": "Sample JSON data",
				},
				{
					"path":        "/more_static/config.txt",
					"type":        "Text",
					"description": "Configuration file",
				},
				{
					"path":        "/favicon.ico",
					"type":        "Icon",
					"description": "Website favicon",
				},
			},
		})
	})

	// File upload endpoint (for demonstration)
	router.POST("/upload", func(c *mux.Context) {
		c.JSON(http.StatusOK, mux.H{
			"message": "File upload endpoint",
			"note":    "This is a demo - actual file upload not implemented",
		})
	})

	fmt.Println("Server starting on :8080")
	fmt.Println("Try these endpoints:")
	fmt.Println("  GET / - Demo page with static file links")
	fmt.Println("  GET /assets/style.css - CSS file")
	fmt.Println("  GET /assets/script.js - JavaScript file")
	fmt.Println("  GET /assets/image.png - Image file")
	fmt.Println("  GET /assets/data.json - JSON file")
	fmt.Println("  GET /more_static/config.txt - Config file")
	fmt.Println("  GET /favicon.ico - Favicon")
	fmt.Println("  GET /api/files - List all static files")
	fmt.Println("")
	fmt.Println("Static file directories:")
	fmt.Println("  /assets -> ./assets")
	fmt.Println("  /more_static -> ./my_file_system")
	fmt.Println("  /favicon.ico -> ./resources/favicon.ico")

	// Listen and serve on 0.0.0.0:8080 (following Gin example exactly)
	log.Fatal(router.Run(":8080"))
}

func setupDemoFiles() {
	// Create assets directory
	os.MkdirAll("assets", 0o755)
	os.MkdirAll("my_file_system", 0o755)
	os.MkdirAll("resources", 0o755)

	// Create CSS file
	cssContent := `
body {
    font-family: Arial, sans-serif;
    margin: 40px;
    background-color: #f5f5f5;
}

h1 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

h2 {
    color: #666;
    margin-top: 30px;
}

ul {
    list-style-type: none;
    padding: 0;
}

li {
    margin: 10px 0;
    padding: 10px;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
`
	os.WriteFile("assets/style.css", []byte(cssContent), 0o644)

	// Create JavaScript file
	jsContent := `
console.log('Static file serving demo loaded!');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded');

    // Add click handlers to links
    const links = document.querySelectorAll('a[href^="/assets"], a[href^="/more_static"]');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            console.log('Accessing static file:', this.href);
        });
    });
});
`
	os.WriteFile("assets/script.js", []byte(jsContent), 0o644)

	// Create a simple PNG image (1x1 pixel)
	pngData := []byte{
		0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
		0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
		0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
		0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x0F, 0x00, 0x00,
		0x01, 0x00, 0x01, 0x5C, 0xCD, 0x90, 0x0A, 0x00, 0x00, 0x00, 0x00, 0x49,
		0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82,
	}
	os.WriteFile("assets/image.png", pngData, 0o644)

	// Create JSON file
	jsonContent := `{
    "name": "Static File Demo",
    "version": "1.0.0",
    "description": "Demonstration of static file serving with gnet-mux",
    "features": [
        "Static directory serving",
        "Single file serving",
        "Custom file system serving",
        "MIME type detection",
        "Cache headers"
    ],
    "endpoints": {
        "assets": "/assets/*",
        "more_static": "/more_static/*",
        "favicon": "/favicon.ico"
    }
}`
	os.WriteFile("assets/data.json", []byte(jsonContent), 0o644)

	// Create config file in my_file_system directory
	configContent := `# Configuration File
# This file is served from the my_file_system directory

server.port=8080
server.host=localhost
static.cache_max_age=31536000
static.enable_directory_listing=true

# Demo settings
demo.name=Static File Serving Demo
demo.version=1.0.0
`
	os.WriteFile("my_file_system/config.txt", []byte(configContent), 0o644)

	// Create favicon.ico (simple ICO format)
	icoData := []byte{
		0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x10, 0x10, 0x00, 0x00, 0x01, 0x00,
		0x04, 0x00, 0x28, 0x01, 0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x28, 0x00,
		0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x01, 0x00,
		0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00,
	}
	// Add more ICO data...
	for i := 0; i < 200; i++ {
		icoData = append(icoData, 0x00)
	}
	os.WriteFile("resources/favicon.ico", icoData, 0o644)

	fmt.Println("Demo files created successfully!")
}
