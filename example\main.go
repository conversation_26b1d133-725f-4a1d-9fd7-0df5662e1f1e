//go:build ignore
// +build ignore

package main

import (
	"log"
	"net/http"

	mux "gitlink.org.cn/nanakura/gnet-mux"
)

func main() {
	// 创建一个带有默认中间件的引擎
	r := mux.Default()

	// 添加CORS中间件
	r.Use(mux.CORS())

	// 定义路由
	r.GET("/", func(c *mux.Context) {
		c.J<PERSON>N(http.StatusOK, map[string]interface{}{
			"message": "Hello, World!",
			"status":  "success",
		})
	})

	r.GET("/ping", func(c *mux.Context) {
		c.String(200, "pong")
	})

	r.GET("/user/:id", func(c *mux.Context) {
		id := c.Param("id")
		c.<PERSON>(http.StatusOK, map[string]interface{}{
			"user_id": id,
			"message": "User found",
		})
	})

	// 多个路径参数
	r.GET("/user/:id/post/:postId", func(c *mux.Context) {
		userID := c.Param("id")
		postID := c.Param("postId")
		c.<PERSON>(http.StatusOK, map[string]interface{}{
			"user_id": userID,
			"post_id": postID,
			"message": "User post found",
		})
	})

	// 混合路径参数和固定路径
	r.GET("/api/v1/user/:id/profile", func(c *mux.Context) {
		id := c.Param("id")
		c.JSON(http.StatusOK, map[string]interface{}{
			"user_id": id,
			"profile": map[string]string{
				"name":  "John Doe",
				"email": "<EMAIL>",
			},
		})
	})

	r.GET("/query", func(c *mux.Context) {
		name := c.DefaultQuery("name", "Guest")
		age := c.DefaultQuery("age", "0")
		c.JSON(http.StatusOK, map[string]interface{}{
			"name": name,
			"age":  age,
		})
	})

	r.POST("/user", func(c *mux.Context) {
		name := c.PostForm("name")
		email := c.PostForm("email")

		if name == "" || email == "" {
			c.AbortWithStatusJSON(http.StatusBadRequest, map[string]string{
				"error": "Name and email are required",
			})
			return
		}

		c.JSON(http.StatusOK, map[string]interface{}{
			"message": "User created successfully",
			"user": map[string]string{
				"name":  name,
				"email": email,
			},
		})
	})

	r.PUT("/user/:id", func(c *mux.Context) {
		id := c.Param("id")
		name := c.PostForm("name")
		c.JSON(http.StatusOK, map[string]interface{}{
			"message": "User updated",
			"user_id": id,
			"name":    name,
		})
	})

	r.DELETE("/user/:id", func(c *mux.Context) {
		id := c.Param("id")
		c.JSON(http.StatusOK, map[string]interface{}{
			"message": "User deleted",
			"user_id": id,
		})
	})

	// HTML响应示例
	r.GET("/html", func(c *mux.Context) {
		html := `
		<!DOCTYPE html>
		<html>
		<head>
			<title>Gnet-Mux Example</title>
		</head>
		<body>
			<h1>Welcome to Gnet-Mux!</h1>
			<p>This is a Gin-style web framework built on top of gnet/v2.</p>
		</body>
		</html>
		`
		c.HTML(html)
	})

	// 重定向示例
	r.GET("/redirect", func(c *mux.Context) {
		c.Redirect(http.StatusMovedPermanently, "/")
	})

	// 自定义中间件示例
	r.Use(func(next mux.HandlerFunc) mux.HandlerFunc {
		return func(c *mux.Context) {
			// 在请求处理前执行
			c.Header("X-Custom-Header", "Gnet-Mux")
			next(c)
			// 在请求处理后执行
		}
	})

	// 启动服务器
	log.Println("Starting server on :8080")
	if err := r.Run(":8080"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
