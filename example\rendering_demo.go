//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"log"
	"net/http"
	"strings"

	mux "gitlink.org.cn/nanakura/gnet-mux"
	"google.golang.org/protobuf/types/known/anypb"
)

// Simple protobuf message for demo purposes
// In a real application, you would generate this from .proto files
type SimpleMessage struct {
	Name   string `json:"name"`
	Number int    `json:"number"`
}

// Implement proto.Message interface for demo
func (s *SimpleMessage) Reset()         {}
func (s *SimpleMessage) String() string { return fmt.Sprintf("Name: %s, Number: %d", s.Name, s.Number) }
func (*SimpleMessage) ProtoMessage()    {}

func main() {
	engine := mux.Default()

	// JSON response example (existing functionality)
	engine.GET("/json", func(c *mux.Context) {
		c.JSON(http.StatusOK, mux.H{
			"message": "Hello JSON",
			"status":  "success",
			"data": map[string]interface{}{
				"id":   123,
				"name": "<PERSON>",
			},
		})
	})

	// XML response example (new functionality)
	engine.GET("/xml", func(c *mux.Context) {
		c.XML(http.StatusOK, mux.H{
			"message": "Hello XML",
			"status":  "success",
			"data": map[string]interface{}{
				"id":   123,
				"name": "John Doe",
			},
		})
	})

	// YAML response example (new functionality)
	engine.GET("/yaml", func(c *mux.Context) {
		c.YAML(http.StatusOK, mux.H{
			"message": "Hello YAML",
			"status":  "success",
			"data": map[string]interface{}{
				"id":   123,
				"name": "John Doe",
			},
		})
	})

	// ProtoBuf response example (new functionality)
	// Note: For a real protobuf example, you would use generated proto messages
	engine.GET("/protobuf", func(c *mux.Context) {
		// Using anypb.Any as a simple proto.Message example
		msg := &anypb.Any{
			TypeUrl: "type.googleapis.com/example.SimpleMessage",
			Value:   []byte(`{"name":"John Doe","number":123}`),
		}
		c.ProtoBuf(http.StatusOK, msg)
	})

	// DataFromReader example (new functionality)
	engine.GET("/data-from-reader", func(c *mux.Context) {
		// Simulate getting data from an external source
		data := "This is data from a reader source"
		reader := strings.NewReader(data)

		extraHeaders := map[string]string{
			"Content-Disposition": `attachment; filename="example.txt"`,
			"Cache-Control":       "no-cache",
		}

		c.DataFromReader(http.StatusOK, int64(len(data)), "text/plain", reader, extraHeaders)
	})

	// Example showing different content types
	engine.GET("/content-types", func(c *mux.Context) {
		format := c.DefaultQuery("format", "json")

		data := mux.H{
			"message":   "Hello World",
			"format":    format,
			"timestamp": "2024-01-01T00:00:00Z",
		}

		switch format {
		case "xml":
			c.XML(http.StatusOK, data)
		case "yaml":
			c.YAML(http.StatusOK, data)
		case "json":
			fallthrough
		default:
			c.JSON(http.StatusOK, data)
		}
	})

	// Example with custom headers
	engine.GET("/custom-headers", func(c *mux.Context) {
		c.Header("X-Custom-Header", "Custom Value")
		c.Header("X-API-Version", "v1.0")

		c.JSON(http.StatusOK, mux.H{
			"message": "Response with custom headers",
			"headers": "Check the response headers",
		})
	})

	fmt.Println("Server starting on :8080")
	fmt.Println("Try these endpoints:")
	fmt.Println("  GET /json - JSON response")
	fmt.Println("  GET /xml - XML response")
	fmt.Println("  GET /yaml - YAML response")
	fmt.Println("  GET /protobuf - Protocol Buffer response")
	fmt.Println("  GET /data-from-reader - Data from reader")
	fmt.Println("  GET /content-types?format=json|xml|yaml - Dynamic format")
	fmt.Println("  GET /custom-headers - Response with custom headers")

	log.Fatal(engine.Run(":8080"))
}
