package mux

import (
	"log"
	"time"
)

// Middleware implementations

// Logger returns a logger middleware
func Logger() Middleware {
	return func(next HandlerFunc) HandlerFunc {
		return func(c *Context) {
			start := time.Now()
			path := c.request.URL.Path
			raw := c.request.URL.RawQuery

			next(c)

			latency := time.Since(start)
			clientIP := c.request.RemoteAddr
			method := c.request.Method
			statusCode := c.response.statusCode

			if raw != "" {
				path = path + "?" + raw
			}

			log.Printf("[%s] %s %s %d %v", clientIP, method, path, statusCode, latency)
		}
	}
}

// Recovery returns a recovery middleware
func Recovery() Middleware {
	return func(next HandlerFunc) HandlerFunc {
		return func(c *Context) {
			defer func() {
				if err := recover(); err != nil {
					log.Printf("Panic recovered: %v", err)
					c.AbortWithStatus(500)
				}
			}()
			next(c)
		}
	}
}

// CORS returns a CORS middleware
func CORS() Middleware {
	return func(next HandlerFunc) HandlerFunc {
		return func(c *Context) {
			c.<PERSON>("Access-Control-Allow-Origin", "*")
			c<PERSON><PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

			if c.request.Method == "OPTIONS" {
				c.Status(200)
				c.writeResponse()
				return
			}

			next(c)
		}
	}
}
