package mux

import (
	"bufio"
	"bytes"
	"errors"
	"io"
	"net/http"
	"strconv"
)

// httpCodec handles HTTP request parsing and response writing
type httpCodec struct {
	parser        *httpParser
	contentLength int
	buf           []byte
}

// httpParser is a simple HTTP parser
type httpParser struct {
	headers map[string]string
}

var (
	CRLF      = []byte("\r\n\r\n")
	lastChunk = []byte("0\r\n\r\n")
)

// newHTTPParser creates a new HTTP parser
func newHTTPParser() *httpParser {
	return &httpParser{
		headers: make(map[string]string),
	}
}

// parse parses HTTP request data and returns the next offset, parsed request, and any error
func (hc *httpCodec) parse(data []byte) (int, *http.Request, error) {
	// Find the end of headers
	headerEnd := bytes.Index(data, CRLF)
	if headerEnd == -1 {
		return 0, nil, errors.New("incomplete HTTP request")
	}

	// Parse the request using <PERSON>'s standard library
	reader := bufio.NewReader(bytes.NewReader(data))
	req, err := http.ReadRequest(reader)
	if err != nil {
		return 0, nil, err
	}

	// Calculate the body offset
	bodyOffset := headerEnd + 4

	// Handle Content-Length
	contentLength := hc.getContentLength(req)
	if contentLength > 0 {
		bodyEnd := bodyOffset + contentLength
		if len(data) >= bodyEnd {
			// Create a new request with the body
			body := data[bodyOffset:bodyEnd]
			req.Body = io.NopCloser(bytes.NewReader(body))
			return bodyEnd, req, nil
		}
		return 0, nil, errors.New("incomplete request body")
	}

	// Handle chunked encoding
	if req.Header.Get("Transfer-Encoding") == "chunked" {
		if idx := bytes.Index(data[bodyOffset:], lastChunk); idx != -1 {
			bodyEnd := bodyOffset + idx + 5
			body, _ := io.ReadAll(req.Body)
			req.Body = io.NopCloser(bytes.NewReader(body))
			return bodyEnd, req, nil
		}
		return 0, nil, errors.New("incomplete chunked request")
	}

	// Request without body
	return bodyOffset, req, nil
}

// getContentLength extracts content length from the request
func (hc *httpCodec) getContentLength(req *http.Request) int {
	if hc.contentLength != -1 {
		return hc.contentLength
	}

	contentLengthStr := req.Header.Get("Content-Length")
	if contentLengthStr != "" {
		if length, err := strconv.Atoi(contentLengthStr); err == nil {
			hc.contentLength = length
			return length
		}
	}

	hc.contentLength = 0
	return 0
}

// resetParser resets the parser state
func (hc *httpCodec) resetParser() {
	hc.contentLength = -1
	if hc.parser != nil {
		hc.parser.headers = make(map[string]string)
	}
}

// reset resets the codec state
func (hc *httpCodec) reset() {
	hc.resetParser()
	hc.buf = hc.buf[:0]
}
