//go:build ignore
// +build ignore

package main

import (
	"log"
	"net/http"

	"github.com/gorilla/websocket"
	mux "gitlink.org.cn/nanakura/gnet-mux"
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		return true // Allow connections from any origin
	},
}

func main() {
	// Create a new engine
	r := mux.Default()

	// Serve static files for the WebSocket client
	r.Static("/static", "./static")

	// WebSocket endpoint
	r.GET("/ws", handleWebSocket)

	// Serve a simple HTML page for testing
	r.GET("/", func(c *mux.Context) {
		c.HTML(`<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
</head>
<body>
    <h1>WebSocket Test</h1>
    <div id="messages"></div>
    <input type="text" id="messageInput" placeholder="Type a message...">
    <button onclick="sendMessage()">Send</button>
    
    <script>
        const ws = new WebSocket('ws://localhost:8080/ws');
        const messages = document.getElementById('messages');
        
        ws.onopen = function(event) {
            console.log('Connected to WebSocket');
            addMessage('Connected to server');
        };
        
        ws.onmessage = function(event) {
            console.log('Message from server:', event.data);
            addMessage('Server: ' + event.data);
        };
        
        ws.onclose = function(event) {
            console.log('WebSocket connection closed');
            addMessage('Connection closed');
        };
        
        ws.onerror = function(error) {
            console.log('WebSocket error:', error);
            addMessage('Error: ' + error);
        };
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value;
            if (message) {
                ws.send(message);
                addMessage('You: ' + message);
                input.value = '';
            }
        }
        
        function addMessage(message) {
            const div = document.createElement('div');
            div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
        }
        
        // Send message on Enter key
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>`)
	})

	log.Println("Starting WebSocket demo server on :8080")
	log.Println("Open http://localhost:8080 in your browser to test WebSocket functionality")
	r.Run(":8080")
}

func handleWebSocket(c *mux.Context) {
	// Upgrade the HTTP connection to a WebSocket connection
	conn, err := c.UpgradeWebSocket(&upgrader)
	if err != nil {
		log.Printf("Failed to upgrade to WebSocket: %v", err)
		return
	}
	defer conn.Close()

	log.Println("WebSocket connection established")

	// Send a welcome message
	err = conn.WriteMessage(websocket.TextMessage, []byte("Welcome to the WebSocket server!"))
	if err != nil {
		log.Printf("Failed to send welcome message: %v", err)
		return
	}

	// Handle incoming messages
	for {
		messageType, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		log.Printf("Received message: %s", message)

		// Echo the message back to the client
		err = conn.WriteMessage(messageType, []byte("Echo: "+string(message)))
		if err != nil {
			log.Printf("Failed to send message: %v", err)
			break
		}
	}

	log.Println("WebSocket connection closed")
}
