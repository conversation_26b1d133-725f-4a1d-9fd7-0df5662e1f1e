package mux

import (
	"net/http"
	"strings"
	"testing"
)

func TestCookie(t *testing.T) {
	engine := New()

	// Create a request with cookies
	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	// Add cookies to the request
	req.AddCookie(&http.Cookie{Name: "test_cookie", Value: "test_value"})
	req.AddCookie(&http.Cookie{Name: "another_cookie", Value: "another_value"})

	ctx := engine.createContext(nil, req)

	// Test getting existing cookie
	value, err := ctx.Cookie("test_cookie")
	if err != nil {
		t.<PERSON><PERSON>rf("Expected no error, got %v", err)
	}
	if value != "test_value" {
		t.<PERSON>rrorf("Expected cookie value to be 'test_value', got '%s'", value)
	}

	// Test getting another existing cookie
	value, err = ctx.Cookie("another_cookie")
	if err != nil {
		t.<PERSON>rf("Expected no error, got %v", err)
	}
	if value != "another_value" {
		t.<PERSON><PERSON><PERSON>("Expected cookie value to be 'another_value', got '%s'", value)
	}

	// Test getting non-existent cookie
	value, err = ctx.Cookie("non_existent")
	if err == nil {
		t.Error("Expected error for non-existent cookie")
	}
	if value != "" {
		t.Errorf("Expected empty value for non-existent cookie, got '%s'", value)
	}
}

func TestSetCookie(t *testing.T) {
	engine := New()

	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := engine.createContext(nil, req)

	// Test setting a basic cookie
	ctx.SetCookie("test_cookie", "test_value", 3600, "/", "localhost", false, true)

	// Check that the Set-Cookie header was set
	setCookieHeader := ctx.response.headers["Set-Cookie"]
	if setCookieHeader == "" {
		t.Fatal("Set-Cookie header should be set")
	}

	// Verify cookie attributes
	if !strings.Contains(setCookieHeader, "test_cookie=test_value") {
		t.Error("Cookie name and value should be in Set-Cookie header")
	}

	if !strings.Contains(setCookieHeader, "Max-Age=3600") {
		t.Error("Max-Age should be in Set-Cookie header")
	}

	if !strings.Contains(setCookieHeader, "Path=/") {
		t.Error("Path should be in Set-Cookie header")
	}

	if !strings.Contains(setCookieHeader, "Domain=localhost") {
		t.Error("Domain should be in Set-Cookie header")
	}

	if !strings.Contains(setCookieHeader, "HttpOnly") {
		t.Error("HttpOnly should be in Set-Cookie header")
	}

	// Secure should not be present since we set it to false
	if strings.Contains(setCookieHeader, "Secure") {
		t.Error("Secure should not be in Set-Cookie header when set to false")
	}
}

func TestSetCookieWithDefaults(t *testing.T) {
	engine := New()

	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := engine.createContext(nil, req)

	// Test setting cookie with empty path (should default to "/")
	ctx.SetCookie("default_path_cookie", "value", 0, "", "", false, false)

	setCookieHeader := ctx.response.headers["Set-Cookie"]
	if !strings.Contains(setCookieHeader, "Path=/") {
		t.Error("Path should default to '/' when empty")
	}
}

func TestSetCookieSecure(t *testing.T) {
	engine := New()

	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := engine.createContext(nil, req)

	// Test setting a secure cookie
	ctx.SetCookie("secure_cookie", "secure_value", 3600, "/", "example.com", true, true)

	setCookieHeader := ctx.response.headers["Set-Cookie"]

	if !strings.Contains(setCookieHeader, "Secure") {
		t.Error("Secure should be in Set-Cookie header when set to true")
	}

	if !strings.Contains(setCookieHeader, "HttpOnly") {
		t.Error("HttpOnly should be in Set-Cookie header when set to true")
	}
}

func TestCookieIntegration(t *testing.T) {
	engine := New()

	// Test the complete flow: set cookie in one request, read it in another

	// First request: set cookie
	req1, _ := http.NewRequest("GET", "/set", nil)
	ctx1 := engine.createContext(nil, req1)

	ctx1.SetCookie("integration_test", "integration_value", 3600, "/", "", false, false)

	setCookieHeader := ctx1.response.headers["Set-Cookie"]
	if setCookieHeader == "" {
		t.Fatal("Set-Cookie header should be set")
	}

	// Parse the Set-Cookie header to create a cookie for the next request
	// This simulates what a browser would do
	cookie := &http.Cookie{}
	parts := strings.Split(setCookieHeader, ";")
	for i, part := range parts {
		part = strings.TrimSpace(part)
		if i == 0 {
			// First part is name=value
			nameValue := strings.SplitN(part, "=", 2)
			if len(nameValue) == 2 {
				cookie.Name = nameValue[0]
				cookie.Value = nameValue[1]
			}
		}
	}

	// Second request: read cookie
	req2, _ := http.NewRequest("GET", "/get", nil)
	req2.AddCookie(cookie)
	ctx2 := engine.createContext(nil, req2)

	value, err := ctx2.Cookie("integration_test")
	if err != nil {
		t.Errorf("Expected no error reading cookie, got %v", err)
	}

	if value != "integration_value" {
		t.Errorf("Expected cookie value to be 'integration_value', got '%s'", value)
	}
}

func TestCookieWithSpecialCharacters(t *testing.T) {
	engine := New()

	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := engine.createContext(nil, req)

	// Test setting cookie with special characters (URL encoded)
	ctx.SetCookie("special_cookie", "value%20with%20spaces", 3600, "/", "", false, false)

	setCookieHeader := ctx.response.headers["Set-Cookie"]
	if !strings.Contains(setCookieHeader, "special_cookie=value%20with%20spaces") {
		t.Error("Cookie with special characters should be properly set")
	}
}

func TestMultipleCookies(t *testing.T) {
	engine := New()

	// Create request with multiple cookies
	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	req.AddCookie(&http.Cookie{Name: "cookie1", Value: "value1"})
	req.AddCookie(&http.Cookie{Name: "cookie2", Value: "value2"})
	req.AddCookie(&http.Cookie{Name: "cookie3", Value: "value3"})

	ctx := engine.createContext(nil, req)

	// Test getting all cookies
	value1, err1 := ctx.Cookie("cookie1")
	value2, err2 := ctx.Cookie("cookie2")
	value3, err3 := ctx.Cookie("cookie3")

	if err1 != nil || err2 != nil || err3 != nil {
		t.Error("Should be able to get all cookies without error")
	}

	if value1 != "value1" || value2 != "value2" || value3 != "value3" {
		t.Error("All cookie values should be correct")
	}
}
