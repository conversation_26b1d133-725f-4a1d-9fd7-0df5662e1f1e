//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"log"
	"net/http"

	mux "gitlink.org.cn/nanakura/gnet-mux"
)

func main() {
	// Create router with middleware (exactly like Gin example)
	r := mux.New()
	r.Use(mux.Logger())
	r.Use(mux.Recovery())

	// Fuzzy routing examples (exactly like Gin)
	r.GET("/foo/*any", someHandler)

	// More fuzzy routing examples
	r.GET("/api/*path", apiHandler)
	r.GET("/static/*filepath", staticHandler)
	r.GET("/files/*filename", fileHandler)

	// Mix with regular routes to show priority
	r.GET("/api/health", healthHandler)
	r.GET("/api/version", versionHandler)
	r.GET("/foo/bar", specificHandler)

	// Root wildcard (catch-all)
	r.GET("/catch/*all", catchAllHandler)

	// Demo page
	r.GET("/", func(c *mux.Context) {
		c.HTML(`
<!DOCTYPE html>
<html>
<head>
    <title>Fuzzy Routing Demo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .route { background: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
        .example { background: #e8f4f8; padding: 8px; margin: 5px 0; font-family: monospace; }
        h2 { color: #333; }
        .priority { background: #fff3cd; padding: 10px; margin: 10px 0; border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Fuzzy Routing Demo</h1>
        <p>This demo shows fuzzy routing (wildcard routing) functionality that exactly matches Gin framework patterns.</p>

        <h2>Fuzzy Routes (Wildcard)</h2>

        <div class="route">
            <strong>GET /foo/*any</strong> - Matches anything after /foo/
            <div class="example">Try: <a href="/foo/bar">/foo/bar</a> → any="bar"</div>
            <div class="example">Try: <a href="/foo/bar/baz/qux">/foo/bar/baz/qux</a> → any="bar/baz/qux"</div>
            <div class="example">Try: <a href="/foo/">/foo/</a> → any=""</div>
        </div>

        <div class="route">
            <strong>GET /api/*path</strong> - API catch-all
            <div class="example">Try: <a href="/api/users/123">/api/users/123</a> → path="users/123"</div>
            <div class="example">Try: <a href="/api/posts/456/comments">/api/posts/456/comments</a> → path="posts/456/comments"</div>
        </div>

        <div class="route">
            <strong>GET /static/*filepath</strong> - Static file serving
            <div class="example">Try: <a href="/static/css/style.css">/static/css/style.css</a> → filepath="css/style.css"</div>
            <div class="example">Try: <a href="/static/js/app.js">/static/js/app.js</a> → filepath="js/app.js"</div>
        </div>

        <div class="route">
            <strong>GET /files/*filename</strong> - File downloads
            <div class="example">Try: <a href="/files/documents/report.pdf">/files/documents/report.pdf</a> → filename="documents/report.pdf"</div>
        </div>

        <div class="route">
            <strong>GET /catch/*all</strong> - Root level catch-all
            <div class="example">Try: <a href="/catch/anything/goes/here">/catch/anything/goes/here</a> → all="anything/goes/here"</div>
        </div>

        <h2>Route Priority</h2>
        <div class="priority">
            <strong>Specific routes take precedence over wildcard routes:</strong>
            <div class="example">✓ <a href="/api/health">/api/health</a> → matches specific route (not wildcard)</div>
            <div class="example">✓ <a href="/api/version">/api/version</a> → matches specific route (not wildcard)</div>
            <div class="example">✓ <a href="/foo/bar">/foo/bar</a> → matches specific route (not wildcard)</div>
            <div class="example">✓ <a href="/api/other">/api/other</a> → matches wildcard route /api/*path</div>
        </div>

        <h2>Gin Compatibility</h2>
        <p>This implementation exactly matches Gin framework behavior:</p>
        <pre><code>func InitRouter() *gin.Engine {
    r := gin.New()
    r.Use(gin.Logger())
    r.Use(gin.Recovery())

    r.GET("/foo/*any", someHandler)

    return r
}</code></pre>
    </div>
</body>
</html>`)
	})

	log.Println("Starting fuzzy routing demo server on :8080")
	log.Println("Visit http://localhost:8080 to test fuzzy routing")
	log.Println("Fuzzy routing patterns:")
	log.Println("  GET /foo/*any")
	log.Println("  GET /api/*path")
	log.Println("  GET /static/*filepath")
	log.Println("  GET /files/*filename")
	log.Println("  GET /catch/*all")

	if err := r.Run(":8080"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

// Handler functions (exactly like Gin example)
func someHandler(c *mux.Context) {
	any := c.Param("any")
	c.JSON(http.StatusOK, mux.H{
		"route":   "/foo/*any",
		"any":     any,
		"message": fmt.Sprintf("Fuzzy route matched: any='%s'", any),
	})
}

func apiHandler(c *mux.Context) {
	path := c.Param("path")
	c.JSON(http.StatusOK, mux.H{
		"route":   "/api/*path",
		"path":    path,
		"message": fmt.Sprintf("API fuzzy route: path='%s'", path),
	})
}

func staticHandler(c *mux.Context) {
	filepath := c.Param("filepath")
	c.JSON(http.StatusOK, mux.H{
		"route":    "/static/*filepath",
		"filepath": filepath,
		"message":  fmt.Sprintf("Static file: filepath='%s'", filepath),
		"note":     "In real app, this would serve the actual file",
	})
}

func fileHandler(c *mux.Context) {
	filename := c.Param("filename")
	c.JSON(http.StatusOK, mux.H{
		"route":    "/files/*filename",
		"filename": filename,
		"message":  fmt.Sprintf("File download: filename='%s'", filename),
	})
}

func catchAllHandler(c *mux.Context) {
	all := c.Param("all")
	c.JSON(http.StatusOK, mux.H{
		"route":   "/catch/*all",
		"all":     all,
		"message": fmt.Sprintf("Catch-all route: all='%s'", all),
	})
}

func healthHandler(c *mux.Context) {
	c.JSON(http.StatusOK, mux.H{
		"route":   "/api/health (specific)",
		"status":  "healthy",
		"message": "This specific route takes precedence over /api/*path",
	})
}

func versionHandler(c *mux.Context) {
	c.JSON(http.StatusOK, mux.H{
		"route":   "/api/version (specific)",
		"version": "1.0.0",
		"message": "This specific route takes precedence over /api/*path",
	})
}

func specificHandler(c *mux.Context) {
	c.JSON(http.StatusOK, mux.H{
		"route":   "/foo/bar (specific)",
		"message": "This specific route takes precedence over /foo/*any",
	})
}
