package mux

import (
	"encoding/json"
	"encoding/xml"
	"log"
	"net/http"
	"path"
	"strings"

	"github.com/panjf2000/gnet/v2"
	"google.golang.org/protobuf/proto"
	"gopkg.in/yaml.v3"
)

// HandlerFunc defines the handler function type
type HandlerFunc func(*Context)

// Middleware defines the middleware function type
type Middleware func(HandlerFunc) HandlerFunc

// H is a shortcut for map[string]interface{}
type H map[string]interface{}

// Response represents the HTTP response
type Response struct {
	headers    map[string]string
	statusCode int
	body       []byte
	written    bool
	streaming  bool // indicates if response is in streaming mode
}

// Config represents the configuration for the Engine
type Config struct {
	// Multicore indicates whether the server should be started in multi-core mode
	Multicore bool
	// JSONEncoder is the function used to encode JSON responses
	// If not set, encoding/json.Marshal will be used
	JSONEncoder func(v interface{}) ([]byte, error)

	// JSONDecoder is the function used to decode JSON requests
	// If not set, encoding/json.Unmarshal will be used
	JSONDecoder func(data []byte, v interface{}) error

	// XMLEncoder is the function used to encode XML responses
	// If not set, encoding/xml.Marshal will be used
	XMLEncoder func(v interface{}) ([]byte, error)

	// YAMLEncoder is the function used to encode YAML responses
	// If not set, gopkg.in/yaml.v3.Marshal will be used
	YAMLEncoder func(v interface{}) ([]byte, error)

	// ProtoBufEncoder is the function used to encode ProtoBuf responses
	// If not set, google.golang.org/protobuf/proto.Marshal will be used
	ProtoBufEncoder func(v proto.Message) ([]byte, error)
}

// Engine is the main router engine
type Engine struct {
	gnet.BuiltinEventEngine
	router             *Router
	middleware         []Middleware
	addr               string
	multicore          bool
	eng                gnet.Engine
	jsonEncoder        func(v interface{}) ([]byte, error)
	jsonDecoder        func(data []byte, v interface{}) error
	xmlEncoder         func(v interface{}) ([]byte, error)
	yamlEncoder        func(v interface{}) ([]byte, error)
	protobufEncoder    func(v proto.Message) ([]byte, error)
	MaxMultipartMemory int64 // Maximum memory for multipart forms (default 32 MiB)
}

// New creates a new Engine instance with default configuration
func New() *Engine {
	return NewWithConfig(Config{})
}

// NewWithConfig creates a new Engine instance with custom configuration
func NewWithConfig(config Config) *Engine {
	engine := &Engine{
		router: &Router{
			routes: make(map[string]*routeNode),
		},
		middleware:         make([]Middleware, 0),
		multicore:          config.Multicore,
		MaxMultipartMemory: 32 << 20, // 32 MiB (default like Gin)
	}

	// Set JSON encoder, use default if not provided
	if config.JSONEncoder != nil {
		engine.jsonEncoder = config.JSONEncoder
	} else {
		engine.jsonEncoder = json.Marshal
	}

	// Set JSON decoder, use default if not provided
	if config.JSONDecoder != nil {
		engine.jsonDecoder = config.JSONDecoder
	} else {
		engine.jsonDecoder = json.Unmarshal
	}

	// Set XML encoder, use default if not provided
	if config.XMLEncoder != nil {
		engine.xmlEncoder = config.XMLEncoder
	} else {
		engine.xmlEncoder = xml.Marshal
	}

	// Set YAML encoder, use default if not provided
	if config.YAMLEncoder != nil {
		engine.yamlEncoder = config.YAMLEncoder
	} else {
		engine.yamlEncoder = yaml.Marshal
	}

	// Set ProtoBuf encoder, use default if not provided
	if config.ProtoBufEncoder != nil {
		engine.protobufEncoder = config.ProtoBufEncoder
	} else {
		engine.protobufEncoder = proto.Marshal
	}

	return engine
}

// Default creates a new Engine with default middleware
func Default() *Engine {
	engine := New()
	engine.Use(Logger(), Recovery())
	return engine
}

// Use adds middleware to the engine
func (e *Engine) Use(middleware ...Middleware) {
	e.middleware = append(e.middleware, middleware...)
}

// GET registers a GET route
func (e *Engine) GET(path string, handler HandlerFunc) {
	e.addRoute("GET", path, handler)
}

// POST registers a POST route
func (e *Engine) POST(path string, handler HandlerFunc) {
	e.addRoute("POST", path, handler)
}

// PUT registers a PUT route
func (e *Engine) PUT(path string, handler HandlerFunc) {
	e.addRoute("PUT", path, handler)
}

// DELETE registers a DELETE route
func (e *Engine) DELETE(path string, handler HandlerFunc) {
	e.addRoute("DELETE", path, handler)
}

// PATCH registers a PATCH route
func (e *Engine) PATCH(path string, handler HandlerFunc) {
	e.addRoute("PATCH", path, handler)
}

// HEAD registers a HEAD route
func (e *Engine) HEAD(path string, handler HandlerFunc) {
	e.addRoute("HEAD", path, handler)
}

// OPTIONS registers an OPTIONS route
func (e *Engine) OPTIONS(path string, handler HandlerFunc) {
	e.addRoute("OPTIONS", path, handler)
}

// Group creates a new router group with the given path prefix
func (e *Engine) Group(prefix string) *RouterGroup {
	return &RouterGroup{
		prefix:     prefix,
		middleware: make([]Middleware, 0),
		engine:     e,
	}
}

// Static serves files from the given file system root
func (e *Engine) Static(relativePath, root string) {
	e.StaticFS(relativePath, http.Dir(root))
}

// StaticFS serves files from the given file system
func (e *Engine) StaticFS(relativePath string, fs http.FileSystem) {
	if strings.Contains(relativePath, ":") || strings.Contains(relativePath, "*") {
		panic("URL parameters can not be used when serving a static folder")
	}

	handler := e.createStaticHandler(relativePath, fs)

	// Ensure the path ends with /*filepath for wildcard matching
	urlPattern := relativePath
	if !strings.HasSuffix(urlPattern, "/") {
		urlPattern += "/"
	}
	urlPattern += "*filepath"

	// Register the route for serving static files
	e.GET(urlPattern, handler)
}

// StaticFile serves a single file
func (e *Engine) StaticFile(relativePath, filepath string) {
	if strings.Contains(relativePath, ":") || strings.Contains(relativePath, "*") {
		panic("URL parameters can not be used when serving a static file")
	}

	handler := func(c *Context) {
		c.File(filepath)
	}

	e.GET(relativePath, handler)
}

// addRoute adds a route to the router
func (e *Engine) addRoute(method, path string, handler HandlerFunc) {
	e.router.mu.Lock()
	defer e.router.mu.Unlock()

	if e.router.routes[method] == nil {
		e.router.routes[method] = &routeNode{}
	}

	e.insertRoute(e.router.routes[method], path, handler)
}

// insertRoute inserts a route into the route tree
func (e *Engine) insertRoute(root *routeNode, path string, handler HandlerFunc) {
	if path == "" || path == "/" {
		root.handler = handler
		return
	}

	// Remove leading slash
	if path[0] == '/' {
		path = path[1:]
	}

	segments := strings.Split(path, "/")
	current := root

	for _, segment := range segments {
		if segment == "" {
			continue
		}

		// Check if this is a parameter segment (:param) or wildcard (*param)
		isParam := strings.HasPrefix(segment, ":")
		isWildcard := strings.HasPrefix(segment, "*")
		paramKey := ""

		if isParam {
			paramKey = segment[1:] // Remove the ':' prefix
		} else if isWildcard {
			paramKey = segment[1:] // Remove the '*' prefix
		}

		// Find existing child node
		var child *routeNode
		for _, c := range current.children {
			if (isParam && c.isParam) || (isWildcard && c.isWildcard) ||
				(!isParam && !isWildcard && !c.isParam && !c.isWildcard && c.path == segment) {
				child = c
				break
			}
		}

		// Create new child if not found
		if child == nil {
			child = &routeNode{
				path:       segment,
				isParam:    isParam,
				isWildcard: isWildcard,
				paramKey:   paramKey,
			}
			current.children = append(current.children, child)
		}

		current = child
	}

	current.handler = handler
}

// Run starts the HTTP server
func (e *Engine) Run(addr ...string) error {
	address := ":8080"
	if len(addr) > 0 {
		address = addr[0]
	}

	if !strings.HasPrefix(address, "tcp://") {
		address = "tcp://" + address
	}

	e.addr = address

	log.Printf("Starting server on %s", address)
	return gnet.Run(e, address, gnet.WithMulticore(e.multicore))
}

// OnBoot is called when the server starts
func (e *Engine) OnBoot(eng gnet.Engine) gnet.Action {
	e.eng = eng
	log.Printf("HTTP server with multi-core=%t is listening on %s", e.multicore, e.addr)
	return gnet.None
}

// OnOpen is called when a new connection is opened
func (e *Engine) OnOpen(c gnet.Conn) ([]byte, gnet.Action) {
	c.SetContext(&httpCodec{parser: newHTTPParser()})
	return nil, gnet.None
}

// OnTraffic handles incoming HTTP requests
func (e *Engine) OnTraffic(c gnet.Conn) gnet.Action {
	hc := c.Context().(*httpCodec)
	buf, _ := c.Peek(-1)
	n := len(buf)

	for {
		nextOffset, req, err := hc.parse(buf)
		hc.resetParser()
		if err != nil || req == nil {
			break
		}
		if len(buf) < nextOffset {
			break
		}

		// Handle the request
		ctx := e.createContext(c, req)
		e.handleRequest(ctx)

		buf = buf[nextOffset:]
		if len(buf) == 0 {
			break
		}
	}

	if len(hc.buf) > 0 {
		c.Write(hc.buf)
	}
	hc.reset()
	c.Discard(n - len(buf))
	return gnet.None
}

// createContext creates a new context for the request
func (e *Engine) createContext(c gnet.Conn, req *http.Request) *Context {
	ctx := &Context{
		conn:    c,
		request: req,
		response: &Response{
			headers:    make(map[string]string),
			statusCode: 200,
		},
		params:   make(map[string]string),
		keys:     make(map[string]interface{}),
		handlers: make([]HandlerFunc, 0),
		index:    -1,
		engine:   e, // set engine reference
	}
	return ctx
}

// handleRequest handles the HTTP request
func (e *Engine) handleRequest(ctx *Context) {
	// Find the handler and extract parameters
	handler, params := e.findHandlerWithParams(ctx.request.Method, ctx.request.URL.Path)
	if handler == nil {
		ctx.String(404, "Not Found")
		return
	}

	// Set the extracted parameters
	for key, value := range params {
		ctx.params[key] = value
	}

	// Apply middleware
	ctx.handlers = append(ctx.handlers, handler)
	for i := len(e.middleware) - 1; i >= 0; i-- {
		handler = e.middleware[i](handler)
	}

	// Execute the handler
	handler(ctx)
}

// findHandler finds the handler for the given method and path
func (e *Engine) findHandler(method, path string) HandlerFunc {
	e.router.mu.RLock()
	defer e.router.mu.RUnlock()

	root, ok := e.router.routes[method]
	if !ok {
		return nil
	}

	handler, _ := e.searchRoute(root, path)
	return handler
}

// findHandlerWithParams finds the handler and extracts parameters
func (e *Engine) findHandlerWithParams(method, path string) (HandlerFunc, map[string]string) {
	e.router.mu.RLock()
	defer e.router.mu.RUnlock()

	root, ok := e.router.routes[method]
	if !ok {
		return nil, nil
	}

	return e.searchRoute(root, path)
}

// searchRoute searches for a route in the tree and extracts parameters
func (e *Engine) searchRoute(root *routeNode, path string) (HandlerFunc, map[string]string) {
	if path == "" || path == "/" {
		return root.handler, make(map[string]string)
	}

	// Remove leading slash
	if path[0] == '/' {
		path = path[1:]
	}

	segments := strings.Split(path, "/")
	params := make(map[string]string)

	return e.matchRoute(root, segments, 0, params)
}

// matchRoute recursively matches route segments
func (e *Engine) matchRoute(node *routeNode, segments []string, index int, params map[string]string) (HandlerFunc, map[string]string) {
	// If we've matched all segments
	if index == len(segments) {
		// First try to return the handler for this node
		if node.handler != nil {
			return node.handler, params
		}

		return nil, nil
	}

	segment := segments[index]
	if segment == "" {
		// Empty segment can match wildcard routes (for trailing slash cases)
		for _, child := range node.children {
			if child.isWildcard {
				// For empty segment, wildcard matches empty string
				params[child.paramKey] = ""
				return child.handler, params
			}
		}
		// If no wildcard, continue to next segment
		return e.matchRoute(node, segments, index+1, params)
	}

	// Try to match child nodes in priority order:
	// 1. Exact matches first
	// 2. Parameter matches second
	// 3. Wildcard matches last (as fallback)

	// First, try exact matches
	for _, child := range node.children {
		if !child.isParam && !child.isWildcard && child.path == segment {
			if handler, matchedParams := e.matchRoute(child, segments, index+1, params); handler != nil {
				return handler, matchedParams
			}
		}
	}

	// Second, try parameter matches
	for _, child := range node.children {
		if child.isParam {
			params[child.paramKey] = segment
			if handler, matchedParams := e.matchRoute(child, segments, index+1, params); handler != nil {
				return handler, matchedParams
			}
			// Remove the parameter if no match found
			delete(params, child.paramKey)
		}
	}

	// Finally, try wildcard matches (catch-all)
	for _, child := range node.children {
		if child.isWildcard {
			// Wildcard node matches all remaining segments
			// Filter out empty segments for cleaner wildcard parameter
			remainingSegments := segments[index:]
			var filteredSegments []string
			for _, seg := range remainingSegments {
				if seg != "" {
					filteredSegments = append(filteredSegments, seg)
				}
			}
			remainingPath := strings.Join(filteredSegments, "/")
			params[child.paramKey] = remainingPath
			return child.handler, params
		}
	}

	return nil, nil
}

// Static file serving helper functions

// createStaticHandler creates a handler for serving static files
func (e *Engine) createStaticHandler(relativePath string, fs http.FileSystem) HandlerFunc {
	absolutePath := relativePath
	if relativePath != "/" {
		absolutePath = "/" + strings.Trim(relativePath, "/")
	}

	fileServer := http.StripPrefix(absolutePath, http.FileServer(fs))

	return func(c *Context) {
		file := c.Param("filepath")

		// Security check: prevent directory traversal
		if strings.Contains(file, "..") {
			c.String(403, "Forbidden")
			return
		}

		// Try to open the file to check if it exists
		f, err := fs.Open(file)
		if err != nil {
			c.String(404, "Not Found")
			return
		}
		defer f.Close()

		// Check if it's a directory
		stat, err := f.Stat()
		if err != nil {
			c.String(404, "Not Found")
			return
		}

		if stat.IsDir() {
			// Try to serve index.html
			indexFile := path.Join(file, "index.html")
			indexF, err := fs.Open(indexFile)
			if err != nil {
				c.String(403, "Forbidden")
				return
			}
			indexF.Close()
			file = indexFile
		}

		// Serve the file using http.FileServer
		c.request.URL.Path = absolutePath + "/" + file
		fileServer.ServeHTTP(&responseWriter{ctx: c}, c.request)
	}
}
