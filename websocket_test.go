package mux

import (
	"net/http"
	"strings"
	"testing"

	"github.com/gorilla/websocket"
)

func TestWebSocketUpgrade(t *testing.T) {
	engine := New()

	// Create a simple WebSocket handler
	upgrader := &websocket.Upgrader{
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	engine.GET("/ws", func(c *Context) {
		conn, err := c.UpgradeWebSocket(upgrader)
		if err != nil {
			t.Errorf("Failed to upgrade WebSocket: %v", err)
			return
		}
		defer conn.Close()

		// Echo back any message received
		for {
			messageType, message, err := conn.ReadMessage()
			if err != nil {
				break
			}
			err = conn.WriteMessage(messageType, message)
			if err != nil {
				break
			}
		}
	})

	// Test that the Writer() method returns a valid http.ResponseWriter
	req, err := http.NewRequest("GET", "/ws", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := &Context{
		request: req,
		response: &Response{
			headers:    make(map[string]string),
			statusCode: 200,
		},
		params: make(map[string]string),
		keys:   make(map[string]interface{}),
		engine: engine,
	}

	// Test Writer() method
	writer := ctx.Writer()
	if writer == nil {
		t.Error("Writer() returned nil")
	}

	// Test that Writer() implements http.ResponseWriter interface
	var _ http.ResponseWriter = writer

	// Test Request() method
	request := ctx.Request()
	if request != req {
		t.Error("Request() did not return the expected request")
	}
}

func TestWebSocketUpgradeMethod(t *testing.T) {
	// Create a test request
	req, err := http.NewRequest("GET", "/ws", nil)
	if err != nil {
		t.Fatal(err)
	}

	// Add WebSocket headers
	req.Header.Set("Connection", "Upgrade")
	req.Header.Set("Upgrade", "websocket")
	req.Header.Set("Sec-WebSocket-Version", "13")
	req.Header.Set("Sec-WebSocket-Key", "dGhlIHNhbXBsZSBub25jZQ==")

	ctx := &Context{
		request: req,
		response: &Response{
			headers:    make(map[string]string),
			statusCode: 200,
		},
		params: make(map[string]string),
		keys:   make(map[string]interface{}),
		engine: New(),
	}

	upgrader := &websocket.Upgrader{
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	// Test UpgradeWebSocket method exists and can be called
	// Note: This will fail in the test environment because we don't have a real connection,
	// but we can test that the method exists and the upgrader is called correctly
	_, err = ctx.UpgradeWebSocket(upgrader)
	// We expect an error here because we don't have a real HTTP connection
	if err == nil {
		t.Error("Expected an error when upgrading without a real connection")
	}
}

func TestWebSocketResponseWriter(t *testing.T) {
	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := &Context{
		request: req,
		response: &Response{
			headers:    make(map[string]string),
			statusCode: 200,
		},
		params: make(map[string]string),
		keys:   make(map[string]interface{}),
		engine: New(),
	}

	writer := ctx.Writer()

	// Test Header() method
	header := writer.Header()
	if header == nil {
		t.Error("Header() returned nil")
	}

	// Test setting headers
	header.Set("Test-Header", "test-value")
	// Call WriteHeader to sync headers back to context
	writer.WriteHeader(200)
	if ctx.response.headers["Test-Header"] != "test-value" {
		t.Error("Header was not set correctly")
	}

	// Test WriteHeader method
	writer.WriteHeader(404)
	if ctx.response.statusCode != 404 {
		t.Errorf("Expected status code 404, got %d", ctx.response.statusCode)
	}

	// Test Write method
	data := []byte("test data")
	n, err := writer.Write(data)
	if err != nil {
		t.Errorf("Write failed: %v", err)
	}
	if n != len(data) {
		t.Errorf("Expected to write %d bytes, wrote %d", len(data), n)
	}
	if !strings.Contains(string(ctx.response.body), string(data)) {
		t.Error("Data was not written to response body")
	}
}

func TestWebSocketIntegration(t *testing.T) {
	// This test demonstrates how WebSocket would be used in practice
	engine := New()

	upgrader := &websocket.Upgrader{
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	// Register a WebSocket handler
	engine.GET("/ws", func(c *Context) {
		// This demonstrates the API usage pattern
		conn, err := c.UpgradeWebSocket(upgrader)
		if err != nil {
			c.String(400, "Failed to upgrade to WebSocket")
			return
		}
		defer conn.Close()

		// Handle WebSocket communication
		// (In a real scenario, this would work with actual WebSocket connections)
	})

	// Test that the route was registered correctly
	handler := engine.findHandler("GET", "/ws")
	if handler == nil {
		t.Error("WebSocket handler was not registered")
	}
}
