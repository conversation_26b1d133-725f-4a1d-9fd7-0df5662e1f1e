//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"gitlink.org.cn/nanakura/gnet-mux"
)

// User represents a user with validation tags
type User struct {
	Name     string `json:"name" form:"name" binding:"required,min=2"`
	Email    string `json:"email" form:"email" binding:"required,email"`
	Age      int    `json:"age" form:"age" binding:"required,min=1,max=120"`
	Password string `json:"password" form:"password" binding:"required,min=6"`
}

// LoginForm for login requests
type LoginForm struct {
	Username string `json:"username" form:"username" binding:"required"`
	Password string `json:"password" form:"password" binding:"required,min=6"`
}

// QueryParams for query parameter binding
type QueryParams struct {
	Q     string `form:"q" binding:"required"`
	Limit int    `form:"limit" binding:"min=1,max=100"`
	Page  int    `form:"page" binding:"min=1"`
}

func main() {
	engine := mux.New()

	// JSON binding example
	engine.POST("/users", createUser)

	// Form binding example
	engine.POST("/login", loginHandler)

	// Query binding example
	engine.GET("/search", searchHandler)

	// XML binding example
	engine.POST("/users/xml", createUserXML)

	// Multiple binding example using ShouldBindBodyWith
	engine.POST("/multi-bind", multiBindHandler)

	// Must bind example (automatically returns 400 on error)
	engine.POST("/users/must", createUserMust)

	fmt.Println("Server starting on :8080")
	log.Fatal(engine.Run(":8080"))
}

// createUser demonstrates JSON binding with validation
func createUser(c *mux.Context) {
	var user User
	if err := c.ShouldBindJSON(&user); err != nil {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, map[string]interface{}{
		"message": "User created successfully",
		"user":    user,
	})
}

// loginHandler demonstrates form binding
func loginHandler(c *mux.Context) {
	var form LoginForm
	if err := c.ShouldBind(&form); err != nil {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"error": err.Error(),
		})
		return
	}

	// Simulate login logic
	if form.Username == "admin" && form.Password == "password123" {
		c.JSON(http.StatusOK, map[string]interface{}{
			"message": "Login successful",
			"token":   "fake-jwt-token",
		})
	} else {
		c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"error": "Invalid credentials",
		})
	}
}

// searchHandler demonstrates query parameter binding
func searchHandler(c *mux.Context) {
	var params QueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, map[string]interface{}{
		"message": "Search results",
		"query":   params.Q,
		"limit":   params.Limit,
		"page":    params.Page,
		"results": []string{"result1", "result2", "result3"},
	})
}

// createUserXML demonstrates XML binding
func createUserXML(c *mux.Context) {
	var user User
	if err := c.ShouldBindXML(&user); err != nil {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, map[string]interface{}{
		"message": "User created from XML",
		"user":    user,
	})
}

// multiBindHandler demonstrates ShouldBindBodyWith for multiple bindings
func multiBindHandler(c *mux.Context) {
	var userA User
	var userB User

	// First attempt to bind as User
	if err := c.ShouldBindBodyWith(&userA, mux.JSON); err == nil {
		c.JSON(http.StatusOK, map[string]interface{}{
			"message": "Bound as User A",
			"user":    userA,
		})
		return
	}

	// Second attempt with different structure (reuses cached body)
	if err := c.ShouldBindBodyWith(&userB, mux.JSON); err == nil {
		c.JSON(http.StatusOK, map[string]interface{}{
			"message": "Bound as User B",
			"user":    userB,
		})
		return
	}

	c.JSON(http.StatusBadRequest, map[string]interface{}{
		"error": "Could not bind to any known format",
	})
}

// createUserMust demonstrates Must bind (automatically handles errors)
func createUserMust(c *mux.Context) {
	var user User
	// This will automatically return 400 with error details if binding fails
	c.BindJSON(&user)

	// If we reach here, binding was successful
	c.JSON(http.StatusOK, map[string]interface{}{
		"message": "User created with must bind",
		"user":    user,
		"time":    time.Now(),
	})
}
