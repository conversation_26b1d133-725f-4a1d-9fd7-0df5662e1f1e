package mux

import (
	"errors"
	"fmt"
	"io"
	"mime"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/panjf2000/gnet/v2"
	"google.golang.org/protobuf/proto"
)

// Context represents the context of the current HTTP request
type Context struct {
	conn     gnet.Conn
	request  *http.Request
	response *Response
	params   map[string]string
	keys     map[string]interface{}
	mu       sync.RWMutex
	index    int
	handlers []HandlerFunc
	engine   *Engine // reference to the engine for accessing JSON encoder/decoder
	// queryCache caches the query result from c.Request().URL.Query().
	queryCache url.Values

	// formCache caches c.Request().PostForm, which contains the parsed form data from POST, PATCH,
	// or PUT body parameters.
	formCache url.Values
}

// Context methods

// Param returns the value of the URL parameter
func (c *Context) Param(key string) string {
	return c.params[key]
}

// Query returns the query parameter value
func (c *Context) Query(key string) string {
	return c.request.URL.Query().Get(key)
}

// GetRawData returns stream data.
func (c *Context) GetRawData() ([]byte, error) {
	if c.Request() == nil {
		return nil, errors.New("cannot read nil body")
	}
	return io.ReadAll(c.Request().Body)
}

// PostFormArray returns a slice of strings for a given form key.
// The length of the slice depends on the number of params with the given key.
func (c *Context) PostFormArray(key string) (values []string) {
	values, _ = c.GetPostFormArray(key)
	return
}

// GetPostFormArray returns a slice of strings for a given form key, plus
// a boolean value whether at least one value exists for the given key.
func (c *Context) GetPostFormArray(key string) (values []string, ok bool) {
	c.initFormCache()
	values, ok = c.formCache[key]
	return
}

// GetPostForm is like PostForm(key). It returns the specified key from a POST urlencoded
// form or multipart form when it exists `(value, true)` (even when the value is an empty string),
// otherwise it returns ("", false).
// For example, during a PATCH request to update the user's email:
//
//	    email=<EMAIL>  -->  ("<EMAIL>", true) := GetPostForm("email") // set email to "<EMAIL>"
//		   email=                  -->  ("", true) := GetPostForm("email") // set email to ""
//	                            -->  ("", false) := GetPostForm("email") // do nothing with email
func (c *Context) GetPostForm(key string) (string, bool) {
	if values, ok := c.GetPostFormArray(key); ok {
		return values[0], ok
	}
	return "", false
}

// PostFormMap returns a map for a given form key.
func (c *Context) PostFormMap(key string) (dicts map[string]string) {
	dicts, _ = c.GetPostFormMap(key)
	return
}

// GetPostFormMap returns a map for a given form key, plus a boolean value
// whether at least one value exists for the given key.
func (c *Context) GetPostFormMap(key string) (map[string]string, bool) {
	c.initFormCache()
	return c.get(c.formCache, key)
}

// QueryArray returns a slice of strings for a given query key.
// The length of the slice depends on the number of params with the given key.
func (c *Context) QueryArray(key string) (values []string) {
	values, _ = c.GetQueryArray(key)
	return
}

// GetQueryArray returns a slice of strings for a given query key, plus
// a boolean value whether at least one value exists for the given key.
func (c *Context) GetQueryArray(key string) (values []string, ok bool) {
	c.initQueryCache()
	values, ok = c.queryCache[key]
	return
}

// QueryMap returns a map for a given query key.
func (c *Context) QueryMap(key string) (dicts map[string]string) {
	dicts, _ = c.GetQueryMap(key)
	return
}

// get is an internal method and returns a map which satisfies conditions.
func (c *Context) get(m map[string][]string, key string) (map[string]string, bool) {
	dicts := make(map[string]string)
	exist := false
	for k, v := range m {
		if i := strings.IndexByte(k, '['); i >= 1 && k[0:i] == key {
			if j := strings.IndexByte(k[i+1:], ']'); j >= 1 {
				exist = true
				dicts[k[i+1:][:j]] = v[0]
			}
		}
	}
	return dicts, exist
}

// GetQueryMap returns a map for a given query key, plus a boolean value
// whether at least one value exists for the given key.
func (c *Context) GetQueryMap(key string) (map[string]string, bool) {
	c.initQueryCache()
	return c.get(c.queryCache, key)
}

func (c *Context) initFormCache() {
	if c.formCache == nil {
		c.formCache = make(url.Values)
		req := c.Request()
		if err := req.ParseMultipartForm(c.engine.MaxMultipartMemory); err != nil {
			if !errors.Is(err, http.ErrNotMultipart) {
				// debugPrint("error on parse multipart form array: %v", err)
				c.formCache = url.Values{}
				return
			}
		}
		c.formCache = req.PostForm
	}
}

func (c *Context) initQueryCache() {
	if c.queryCache == nil {
		if c.Request() != nil {
			c.queryCache = c.request.URL.Query()
		} else {
			c.queryCache = url.Values{}
		}
	}
}

// GetQuery is like Query(), it returns the keyed url query value
// if it exists `(value, true)` (even when the value is an empty string),
// otherwise it returns `("", false)`.
// It is shortcut for `c.Request().URL.Query().Get(key)`
//
//	GET /?name=Manu&lastname=
//	("Manu", true) == c.GetQuery("name")
//	("", false) == c.GetQuery("id")
//	("", true) == c.GetQuery("lastname")
func (c *Context) GetQuery(key string) (string, bool) {
	if values, ok := c.GetQueryArray(key); ok {
		return values[0], ok
	}
	return "", false
}

// DefaultQuery returns the query parameter value or default value
func (c *Context) DefaultQuery(key, defaultValue string) string {
	if value := c.Query(key); value != "" {
		return value
	}
	return defaultValue
}

// PostForm returns the form parameter value
func (c *Context) PostForm(key string) string {
	return c.request.FormValue(key)
}

// DefaultPostForm returns the form parameter value or default value
func (c *Context) DefaultPostForm(key, defaultValue string) string {
	if value := c.PostForm(key); value != "" {
		return value
	}
	return defaultValue
}

// FormFile returns the first file for the provided form key
func (c *Context) FormFile(name string) (*multipart.FileHeader, error) {
	if c.request.MultipartForm == nil {
		if err := c.request.ParseMultipartForm(c.engine.MaxMultipartMemory); err != nil {
			return nil, err
		}
	}
	f, fh, err := c.request.FormFile(name)
	if err != nil {
		return nil, err
	}
	f.Close()
	return fh, nil
}

// MultipartForm returns the parsed multipart form, including file uploads
func (c *Context) MultipartForm() (*multipart.Form, error) {
	if err := c.request.ParseMultipartForm(c.engine.MaxMultipartMemory); err != nil {
		return nil, err
	}
	return c.request.MultipartForm, nil
}

// SaveUploadedFile uploads the form file to specific dst
func (c *Context) SaveUploadedFile(file *multipart.FileHeader, dst string) error {
	src, err := file.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	// Create the destination directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(dst), 0o755); err != nil {
		return err
	}

	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()

	_, err = io.Copy(out, src)
	return err
}

// GetHeader returns the request header value
func (c *Context) GetHeader(key string) string {
	return c.request.Header.Get(key)
}

// Request returns the underlying http.Request
func (c *Context) Request() *http.Request {
	return c.request
}

// Writer returns an http.ResponseWriter compatible interface for websocket upgrading
func (c *Context) Writer() http.ResponseWriter {
	return &responseWriter{ctx: c}
}

// UpgradeWebSocket upgrades the HTTP connection to a WebSocket connection using the provided upgrader
// This method follows the Gin framework pattern for WebSocket upgrades
func (c *Context) UpgradeWebSocket(upgrader *websocket.Upgrader) (*websocket.Conn, error) {
	return upgrader.Upgrade(c.Writer(), c.Request(), nil)
}

// Cookie returns the named cookie provided in the request
func (c *Context) Cookie(name string) (string, error) {
	cookie, err := c.request.Cookie(name)
	if err != nil {
		return "", err
	}
	return cookie.Value, nil
}

// SetCookie adds a Set-Cookie header to the ResponseWriter's headers
func (c *Context) SetCookie(name, value string, maxAge int, path, domain string, secure, httpOnly bool) {
	if path == "" {
		path = "/"
	}

	cookie := &http.Cookie{
		Name:     name,
		Value:    value,
		MaxAge:   maxAge,
		Path:     path,
		Domain:   domain,
		Secure:   secure,
		HttpOnly: httpOnly,
	}

	c.Header("Set-Cookie", cookie.String())
}

// Header sets the response header
func (c *Context) Header(key, value string) {
	c.response.headers[key] = value
}

// Status sets the response status code
func (c *Context) Status(code int) *Context {
	c.response.statusCode = code
	return c
}

// Set stores a key-value pair in the context
func (c *Context) Set(key string, value interface{}) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.keys[key] = value
}

// Get retrieves a value from the context
func (c *Context) Get(key string) (interface{}, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	value, exists := c.keys[key]
	return value, exists
}

// MustGet retrieves a value from the context or panics
func (c *Context) MustGet(key string) interface{} {
	if value, exists := c.Get(key); exists {
		return value
	}
	panic("Key \"" + key + "\" does not exist")
}

// String sends a string response
func (c *Context) String(code int, format string, values ...interface{}) {
	c.Header("Content-Type", "text/plain; charset=utf-8")
	body := fmt.Sprintf(format, values...)
	c.Status(code)
	c.response.body = []byte(body)
	c.writeResponse()
}

// JSON sends a JSON response
func (c *Context) JSON(code int, obj interface{}) {
	c.Header("Content-Type", "application/json; charset=utf-8")
	body, err := c.engine.jsonEncoder(obj)
	if err != nil {
		c.String(500, "Internal Server Error")
		return
	}
	c.Status(code)
	c.response.body = body
	c.writeResponse()
}

// XML sends an XML response
func (c *Context) XML(code int, obj interface{}) {
	c.Header("Content-Type", "application/xml; charset=utf-8")
	body, err := c.engine.xmlEncoder(obj)
	if err != nil {
		c.Status(500)
		c.response.body = []byte("Internal Server Error")
		c.writeResponse()
		return
	}
	c.Status(code)
	c.response.body = body
	c.writeResponse()
}

// YAML sends a YAML response
func (c *Context) YAML(code int, obj interface{}) {
	c.Header("Content-Type", "application/x-yaml; charset=utf-8")
	body, err := c.engine.yamlEncoder(obj)
	if err != nil {
		c.Status(500)
		c.response.body = []byte("Internal Server Error")
		c.writeResponse()
		return
	}
	c.Status(code)
	c.response.body = body
	c.writeResponse()
}

// ProtoBuf sends a Protocol Buffer response
func (c *Context) ProtoBuf(code int, obj proto.Message) {
	c.Header("Content-Type", "application/x-protobuf")
	body, err := c.engine.protobufEncoder(obj)
	if err != nil {
		c.Status(500)
		c.response.body = []byte("Internal Server Error")
		c.writeResponse()
		return
	}
	c.Status(code)
	c.response.body = body
	c.writeResponse()
}

// HTML sends an HTML response
func (c *Context) HTML(html string) {
	c.Header("Content-Type", "text/html; charset=utf-8")
	c.response.body = []byte(html)
	c.writeResponse()
}

// Data sends raw data response
func (c *Context) Data(contentType string, data []byte) {
	c.Header("Content-Type", contentType)
	c.response.body = data
	c.writeResponse()
}

// DataFromReader sends data from a reader with optional extra headers
func (c *Context) DataFromReader(code int, contentLength int64, contentType string, reader io.Reader, extraHeaders map[string]string) {
	c.Status(code)
	c.Header("Content-Type", contentType)

	// Set extra headers
	for key, value := range extraHeaders {
		c.Header(key, value)
	}

	// Read data from reader
	data, err := io.ReadAll(reader)
	if err != nil {
		c.Status(500)
		c.response.body = []byte("Internal Server Error")
		c.writeResponse()
		return
	}

	c.response.body = data
	c.writeResponse()
}

// File sends a file response
func (c *Context) File(filepath string) {
	// Check if file exists
	info, err := os.Stat(filepath)
	if err != nil {
		if os.IsNotExist(err) {
			c.String(404, "Not Found")
		} else {
			c.String(500, "Internal Server Error")
		}
		return
	}

	// Check if it's a directory
	if info.IsDir() {
		c.String(403, "Forbidden")
		return
	}

	// Read the file
	data, err := os.ReadFile(filepath)
	if err != nil {
		c.String(500, "Internal Server Error")
		return
	}

	// Detect content type
	contentType := mime.TypeByExtension(path.Ext(filepath))
	if contentType == "" {
		contentType = http.DetectContentType(data)
	}

	// Set headers
	c.Header("Content-Type", contentType)
	c.Header("Content-Length", fmt.Sprintf("%d", len(data)))

	// Set cache headers
	c.Header("Cache-Control", "public, max-age=31536000")
	c.Header("Last-Modified", info.ModTime().UTC().Format(http.TimeFormat))

	// Send the file
	c.response.body = data
	c.writeResponse()
}

// Redirect sends a redirect response
func (c *Context) Redirect(code int, location string) {
	c.Header("Location", location)
	c.Status(code)
	c.writeResponse()
}

// Stream sends a streaming response using chunked transfer encoding
// The step function receives an io.Writer and should return true to continue streaming, false to stop
func (c *Context) Stream(step func(w io.Writer) bool) {
	// Prevent multiple response writes
	if c.response.written {
		return
	}

	// Mark as streaming to prevent other response methods from interfering
	c.response.streaming = true
	c.response.written = true

	// For testing, if conn is nil, just return
	if c.conn == nil {
		return
	}

	hc, ok := c.conn.Context().(*httpCodec)
	if !ok || hc == nil {
		return
	}

	// Write HTTP status line and headers for chunked response
	c.writeStreamingHeaders(hc)

	// Create a streaming writer that writes chunks directly to the connection
	writer := &streamWriter{
		conn: c.conn,
		hc:   hc,
	}

	// Execute the streaming function
	for step(writer) {
		// Continue streaming as long as step returns true
		// The step function is responsible for writing data and controlling the flow
	}

	// Write the final chunk to end the stream
	writer.writeEndChunk()
}

// writeStreamingHeaders writes the HTTP headers for streaming response
func (c *Context) writeStreamingHeaders(hc *httpCodec) {
	// Status line
	hc.buf = append(hc.buf, fmt.Sprintf("HTTP/1.1 %d %s\r\n", c.response.statusCode, http.StatusText(c.response.statusCode))...)

	// Standard headers
	hc.buf = append(hc.buf, "Server: gnet-mux\r\n"...)
	hc.buf = append(hc.buf, "Date: "...)
	hc.buf = time.Now().AppendFormat(hc.buf, "Mon, 02 Jan 2006 15:04:05 GMT")
	hc.buf = append(hc.buf, "\r\n"...)

	// Set Transfer-Encoding to chunked for streaming
	hc.buf = append(hc.buf, "Transfer-Encoding: chunked\r\n"...)

	// Write custom headers
	for key, value := range c.response.headers {
		hc.buf = append(hc.buf, key...)
		hc.buf = append(hc.buf, ": "...)
		hc.buf = append(hc.buf, value...)
		hc.buf = append(hc.buf, "\r\n"...)
	}

	// End headers with CRLF
	hc.buf = append(hc.buf, "\r\n"...)

	// Flush headers to connection
	c.conn.Write(hc.buf)
	hc.buf = hc.buf[:0] // Reset buffer
}

// writeResponse writes the response to the connection
func (c *Context) writeResponse() {
	if c.response.written || c.response.streaming {
		return
	}
	c.response.written = true

	// For testing, if conn is nil, just return
	if c.conn == nil {
		return
	}

	hc, ok := c.conn.Context().(*httpCodec)
	if !ok || hc == nil {
		return
	}

	// Status line
	hc.buf = append(hc.buf, fmt.Sprintf("HTTP/1.1 %d %s\r\n", c.response.statusCode, http.StatusText(c.response.statusCode))...)

	// Headers
	hc.buf = append(hc.buf, "Server: gnet-mux\r\n"...)
	hc.buf = append(hc.buf, "Date: "...)
	hc.buf = time.Now().AppendFormat(hc.buf, "Mon, 02 Jan 2006 15:04:05 GMT")
	hc.buf = append(hc.buf, "\r\n"...)

	for key, value := range c.response.headers {
		hc.buf = append(hc.buf, key...)
		hc.buf = append(hc.buf, ": "...)
		hc.buf = append(hc.buf, value...)
		hc.buf = append(hc.buf, "\r\n"...)
	}

	// Content-Length
	hc.buf = append(hc.buf, "Content-Length: "...)
	hc.buf = append(hc.buf, strconv.Itoa(len(c.response.body))...)
	hc.buf = append(hc.buf, "\r\n\r\n"...)

	// Body
	hc.buf = append(hc.buf, c.response.body...)
}

// Next executes the next handler in the chain
func (c *Context) Next() {
	c.index++
	if c.index < len(c.handlers) {
		c.handlers[c.index](c)
	}
}

// Abort prevents pending handlers from being called
func (c *Context) Abort() {
	c.index = len(c.handlers)
}

// AbortWithStatus aborts with a status code
func (c *Context) AbortWithStatus(code int) {
	c.Status(code)
	c.Abort()
}

// AbortWithStatusJSON aborts with a JSON response
func (c *Context) AbortWithStatusJSON(code int, obj interface{}) {
	c.Status(code)
	c.JSON(code, obj)
	c.Abort()
}
